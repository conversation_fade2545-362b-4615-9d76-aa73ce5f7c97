# Generated by Django 4.2.21 on 2025-06-28 11:17

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('image', models.ImageField(blank=True, null=True, upload_to='users/', verbose_name='صورة المستخدم')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('is_inspection_technician', models.BooleanField(default=False, verbose_name='فني معاينة')),
                ('default_theme', models.CharField(default='default', max_length=50, verbose_name='الثيم الافتراضي')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مستخدم',
                'verbose_name_plural': 'المستخدمين',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='AboutPageSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='عن النظام', max_length=100, verbose_name='عنوان الصفحة')),
                ('subtitle', models.CharField(default='نظام إدارة المصنع والعملاء', max_length=200, verbose_name='العنوان الفرعي')),
                ('system_version', models.CharField(default='1.0.0', editable=False, max_length=50, verbose_name='إصدار النظام')),
                ('system_release_date', models.CharField(default='2025-04-30', editable=False, max_length=50, verbose_name='تاريخ الإطلاق')),
                ('system_developer', models.CharField(default='zakee tahawi', editable=False, max_length=100, verbose_name='المطور')),
                ('system_description', models.TextField(default='نظام متكامل لإدارة العملاء والمبيعات والإنتاج والمخزون', verbose_name='وصف النظام')),
            ],
            options={
                'verbose_name': 'إعدادات صفحة عن النظام',
                'verbose_name_plural': 'إعدادات صفحة عن النظام',
            },
        ),
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True)),
                ('name', models.CharField(max_length=100)),
                ('address', models.TextField(blank=True, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('is_main_branch', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'فرع',
                'verbose_name_plural': 'الفروع',
            },
        ),
        migrations.CreateModel(
            name='CompanyInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('version', models.CharField(blank=True, default='1.0.0', editable=False, max_length=50, verbose_name='إصدار النظام')),
                ('release_date', models.CharField(blank=True, default='2025-04-30', editable=False, max_length=50, verbose_name='تاريخ الإطلاق')),
                ('developer', models.CharField(blank=True, default='zakee tahawi', editable=False, max_length=100, verbose_name='المطور')),
                ('working_hours', models.CharField(blank=True, default='', max_length=100, verbose_name='ساعات العمل')),
                ('name', models.CharField(default='Elkhawaga', max_length=200, verbose_name='اسم الشركة')),
                ('copyright_text', models.CharField(blank=True, default='جميع الحقوق محفوظة لشركة الخواجة للستائر والمفروشات تطوير zakee tahawi', max_length=255, verbose_name='نص حقوق النشر')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='company_logos/')),
                ('address', models.TextField(blank=True, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True)),
                ('commercial_register', models.CharField(blank=True, max_length=50, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('social_links', models.JSONField(blank=True, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('facebook', models.URLField(blank=True, null=True)),
                ('twitter', models.URLField(blank=True, null=True)),
                ('instagram', models.URLField(blank=True, null=True)),
                ('linkedin', models.URLField(blank=True, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('vision', models.TextField(blank=True, null=True)),
                ('mission', models.TextField(blank=True, null=True)),
                ('primary_color', models.CharField(blank=True, max_length=20, null=True)),
                ('secondary_color', models.CharField(blank=True, max_length=20, null=True)),
                ('accent_color', models.CharField(blank=True, max_length=20, null=True)),
            ],
            options={
                'verbose_name': 'معلومات الشركة',
                'verbose_name_plural': 'معلومات الشركة',
            },
        ),
        migrations.CreateModel(
            name='ContactFormSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(default='اتصل بنا', max_length=100, verbose_name='عنوان الصفحة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الصفحة')),
                ('company_name', models.CharField(default='Elkhawaga', max_length=200, verbose_name='اسم الشركة')),
                ('contact_email', models.EmailField(default='<EMAIL>', max_length=254, verbose_name='البريد الإلكتروني للاتصال')),
                ('contact_phone', models.CharField(default='+20 ************', max_length=20, verbose_name='رقم الهاتف للاتصال')),
                ('contact_address', models.TextField(blank=True, null=True, verbose_name='عنوان المكتب')),
                ('contact_hours', models.CharField(default='9 صباحاً - 5 مساءً', max_length=100, verbose_name='ساعات العمل')),
                ('form_title', models.CharField(default='نموذج الاتصال', max_length=100, verbose_name='عنوان النموذج')),
                ('form_success_message', models.CharField(default='تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.', max_length=200, verbose_name='رسالة النجاح')),
                ('form_error_message', models.CharField(default='يرجى ملء جميع الحقول المطلوبة.', max_length=200, verbose_name='رسالة الخطأ')),
            ],
            options={
                'verbose_name': 'إعدادات نموذج الاتصال',
                'verbose_name_plural': 'إعدادات نموذج الاتصال',
            },
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='الرمز')),
                ('department_type', models.CharField(choices=[('administration', 'إدارة'), ('department', 'قسم'), ('unit', 'وحدة')], default='department', max_length=20, verbose_name='النوع')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('icon', models.CharField(blank=True, help_text='Font Awesome icon name', max_length=50, null=True, verbose_name='الأيقونة')),
                ('url_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='اسم الرابط')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('is_core', models.BooleanField(default=False, help_text='حدد هذا الخيار إذا كان هذا القسم من أقسام النظام الأساسية التي لا يمكن حذفها أو تعديلها', verbose_name='قسم أساسي')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='الترتيب')),
                ('has_pages', models.BooleanField(default=False, help_text='حدد هذا الخيار إذا كان هذا القسم يحتوي على صفحات متعددة', verbose_name='يحتوي على صفحات')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to=settings.AUTH_USER_MODEL, verbose_name='المدير')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='accounts.department', verbose_name='القسم الرئيسي')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='FooterSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('left_column_title', models.CharField(default='عن الشركة', max_length=100, verbose_name='عنوان العمود الأيسر')),
                ('left_column_text', models.TextField(default='نظام متكامل لإدارة العملاء والمبيعات والإنتاج والمخزون', verbose_name='نص العمود الأيسر')),
                ('middle_column_title', models.CharField(default='روابط سريعة', max_length=100, verbose_name='عنوان العمود الأوسط')),
                ('right_column_title', models.CharField(default='تواصل معنا', max_length=100, verbose_name='عنوان العمود الأيمن')),
            ],
            options={
                'verbose_name': 'إعدادات تذييل الصفحة',
                'verbose_name_plural': 'إعدادات تذييل الصفحة',
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الدور')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف الدور')),
                ('is_system_role', models.BooleanField(default=False, help_text='تحديد ما إذا كان هذا الدور من أدوار النظام الأساسية التي لا يمكن تعديلها', verbose_name='دور نظام')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('permissions', models.ManyToManyField(blank=True, to='auth.permission', verbose_name='الصلاحيات')),
            ],
            options={
                'verbose_name': 'دور',
                'verbose_name_plural': 'الأدوار',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='نظام الخواجه', max_length=100, verbose_name='اسم النظام')),
                ('currency', models.CharField(choices=[('SAR', 'ريال سعودي'), ('EGP', 'جنيه مصري'), ('USD', 'دولار أمريكي'), ('EUR', 'يورو'), ('AED', 'درهم إماراتي'), ('KWD', 'دينار كويتي'), ('QAR', 'ريال قطري'), ('BHD', 'دينار بحريني'), ('OMR', 'ريال عماني')], default='SAR', max_length=3, verbose_name='العملة')),
                ('version', models.CharField(default='1.0.0', max_length=20, verbose_name='إصدار النظام')),
                ('enable_notifications', models.BooleanField(default=True, verbose_name='تفعيل الإشعارات')),
                ('enable_email_notifications', models.BooleanField(default=False, verbose_name='تفعيل إشعارات البريد الإلكتروني')),
                ('items_per_page', models.PositiveIntegerField(default=20, verbose_name='عدد العناصر في الصفحة')),
                ('low_stock_threshold', models.PositiveIntegerField(default=20, verbose_name='حد المخزون المنخفض (%)')),
                ('enable_analytics', models.BooleanField(default=True, verbose_name='تفعيل التحليلات')),
                ('maintenance_mode', models.BooleanField(default=False, verbose_name='وضع الصيانة')),
                ('maintenance_message', models.TextField(blank=True, verbose_name='رسالة الصيانة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعدادات النظام',
                'verbose_name_plural': 'إعدادات النظام',
            },
        ),
        migrations.CreateModel(
            name='Salesperson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم البائع')),
                ('employee_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الوظيفي')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='salespersons', to='accounts.branch', verbose_name='الفرع')),
            ],
            options={
                'verbose_name': 'بائع',
                'verbose_name_plural': 'البائعون',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية')], default='medium', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('is_read', models.BooleanField(default=False)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('read_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='read_notifications', to=settings.AUTH_USER_MODEL)),
                ('sender', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sent_notifications', to=settings.AUTH_USER_MODEL)),
                ('sender_department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sent_notifications', to='accounts.department')),
                ('target_branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounts.branch')),
                ('target_department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounts.department')),
                ('target_users', models.ManyToManyField(blank=True, related_name='received_notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'إشعار',
                'verbose_name_plural': 'الإشعارات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FormField',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('form_type', models.CharField(choices=[('customer', 'نموذج العميل'), ('order', 'نموذج الطلب'), ('inspection', 'نموذج المعاينة'), ('installation', 'نموذج التركيب'), ('product', 'نموذج المنتج')], max_length=20)),
                ('field_name', models.CharField(max_length=100)),
                ('field_label', models.CharField(max_length=200)),
                ('field_type', models.CharField(choices=[('text', 'نص'), ('number', 'رقم'), ('email', 'بريد إلكتروني'), ('phone', 'هاتف'), ('date', 'تاريخ'), ('select', 'قائمة اختيار'), ('checkbox', 'مربع اختيار'), ('radio', 'زر اختيار'), ('textarea', 'منطقة نص'), ('file', 'ملف')], max_length=20)),
                ('required', models.BooleanField(default=False)),
                ('enabled', models.BooleanField(default=True)),
                ('order', models.PositiveIntegerField(default=0)),
                ('choices', models.TextField(blank=True, help_text='قائمة الخيارات مفصولة بفواصل (للحقول من نوع select, radio, checkbox)', null=True)),
                ('default_value', models.CharField(blank=True, max_length=255, null=True)),
                ('help_text', models.CharField(blank=True, max_length=255, null=True)),
                ('min_length', models.PositiveIntegerField(blank=True, null=True)),
                ('max_length', models.PositiveIntegerField(blank=True, null=True)),
                ('min_value', models.FloatField(blank=True, null=True)),
                ('max_value', models.FloatField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'حقل نموذج',
                'verbose_name_plural': 'حقول النماذج',
                'unique_together': {('form_type', 'field_name')},
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الموظف')),
                ('employee_id', models.CharField(max_length=50, unique=True, verbose_name='رقم الموظف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='accounts.branch', verbose_name='الفرع')),
            ],
            options={
                'verbose_name': 'موظف',
                'verbose_name_plural': 'الموظفون',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BranchMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='نص الرسالة')),
                ('message_type', models.CharField(choices=[('welcome', 'رسالة ترحيبية'), ('goal', 'هدف'), ('announcement', 'إعلان'), ('holiday', 'إجازة')], default='announcement', max_length=20, verbose_name='نوع الرسالة')),
                ('color', models.CharField(default='primary', max_length=50, verbose_name='لون الرسالة')),
                ('icon', models.CharField(default='fas fa-bell', max_length=50, verbose_name='الأيقونة')),
                ('start_date', models.DateTimeField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateTimeField(verbose_name='تاريخ النهاية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='messages', to='accounts.branch', verbose_name='الفرع')),
            ],
            options={
                'verbose_name': 'رسالة الفرع',
                'verbose_name_plural': 'رسائل الفروع',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('عميل', 'عميل'), ('طلب', 'طلب'), ('مخزون', 'مخزون'), ('تركيب', 'تركيب')], max_length=20)),
                ('description', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddField(
            model_name='user',
            name='branch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='users', to='accounts.branch', verbose_name='الفرع'),
        ),
        migrations.AddField(
            model_name='user',
            name='departments',
            field=models.ManyToManyField(blank=True, related_name='users', to='accounts.department', verbose_name='الأقسام'),
        ),
        migrations.AddField(
            model_name='user',
            name='groups',
            field=models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups'),
        ),
        migrations.AddField(
            model_name='user',
            name='user_permissions',
            field=models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions'),
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإسناد')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to='accounts.role', verbose_name='الدور')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'دور المستخدم',
                'verbose_name_plural': 'أدوار المستخدمين',
                'unique_together': {('user', 'role')},
            },
        ),
    ]
