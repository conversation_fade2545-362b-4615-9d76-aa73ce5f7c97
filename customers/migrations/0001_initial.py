# Generated by Django 4.2.21 on 2025-06-28 11:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='الرمز')),
                ('name', models.CharField(db_index=True, max_length=50, verbose_name='اسم النوع')),
                ('description', models.TextField(blank=True, verbose_name='وصف النوع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'نوع العميل',
                'verbose_name_plural': 'أنواع العملاء',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['code'], name='customer_type_code_idx'), models.Index(fields=['name'], name='customer_type_name_idx')],
            },
        ),
        migrations.CreateModel(
            name='CustomerCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=50, verbose_name='اسم التصنيف')),
                ('description', models.TextField(blank=True, verbose_name='وصف التصنيف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'تصنيف العملاء',
                'verbose_name_plural': 'تصنيفات العملاء',
                'ordering': ['name'],
                'indexes': [models.Index(fields=['name'], name='customer_cat_name_idx')],
            },
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(blank=True, max_length=10, unique=True, verbose_name='كود العميل')),
                ('image', models.ImageField(blank=True, null=True, upload_to='customers/images/%Y/%m/', verbose_name='صورة العميل')),
                ('customer_type', models.CharField(default='retail', max_length=20, verbose_name='نوع العميل')),
                ('name', models.CharField(db_index=True, max_length=200, verbose_name='اسم العميل')),
                ('phone', models.CharField(db_index=True, max_length=20, verbose_name='رقم الهاتف')),
                ('phone2', models.CharField(blank=True, help_text='رقم هاتف إضافي اختياري', max_length=20, null=True, verbose_name='رقم الهاتف الثاني')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('interests', models.TextField(blank=True, help_text='اكتب اهتمامات العميل وتفضيلاته', verbose_name='اهتمامات العميل')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('blocked', 'محظور')], db_index=True, default='active', max_length=10, verbose_name='الحالة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('branch', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='customers', to='accounts.branch', verbose_name='الفرع')),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customers', to='customers.customercategory', verbose_name='تصنيف العميل')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customers_created', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'عميل',
                'verbose_name_plural': 'سجل العملاء',
                'ordering': ['-created_at'],
                'permissions': [('view_customer_reports', 'Can view customer reports'), ('export_customer_data', 'Can export customer data')],
            },
        ),
        migrations.CreateModel(
            name='CustomerNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('note', models.TextField(verbose_name='الملاحظة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_notes_created', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notes_history', to='customers.customer', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'ملاحظة العميل',
                'verbose_name_plural': 'ملاحظات العملاء',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['customer', 'created_at'], name='customer_note_idx'), models.Index(fields=['created_by'], name='customer_note_creator_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['code'], name='cust_code_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['name'], name='cust_name_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['phone', 'phone2'], name='cust_phones_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['email'], name='cust_email_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['status'], name='cust_status_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['customer_type'], name='cust_type_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['created_at'], name='cust_created_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['updated_at'], name='cust_updated_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['branch', 'status', 'customer_type'], name='cust_br_st_type_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['name', 'phone', 'email'], name='cust_search_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(fields=['created_by', 'branch'], name='cust_creator_branch_idx'),
        ),
        migrations.AddIndex(
            model_name='customer',
            index=models.Index(condition=models.Q(('status', 'active')), fields=['name', 'phone'], name='cust_active_idx'),
        ),
    ]
