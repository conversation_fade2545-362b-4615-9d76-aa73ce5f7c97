# Generated by Django 4.2.21 on 2025-06-28 11:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0001_initial'),
        ('accounts', '0001_initial'),
        ('customers', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ExtendedOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_type', models.CharField(choices=[('goods', 'سلع'), ('services', 'خدمات')], max_length=10, verbose_name='نوع الطلب')),
                ('goods_type', models.CharField(blank=True, choices=[('accessories', 'اكسسوار'), ('fabric', 'قماش')], max_length=15, null=True, verbose_name='نوع السلعة')),
                ('service_type', models.CharField(blank=True, choices=[('inspection', 'معاينة'), ('tailoring', 'تفصيل'), ('installation', 'تركيب'), ('transport', 'نقل')], max_length=15, null=True, verbose_name='نوع الخدمة')),
                ('invoice_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الفاتورة')),
                ('contract_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم العقد')),
                ('payment_verified', models.BooleanField(default=False, verbose_name='تم التحقق من السداد')),
                ('branch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='extended_orders', to='accounts.branch', verbose_name='الفرع')),
            ],
            options={
                'verbose_name': 'معلومات إضافية للطلب',
                'verbose_name_plural': 'معلومات إضافية للطلبات',
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('delivery_type', models.CharField(choices=[('home', 'توصيل للمنزل'), ('branch', 'استلام من الفرع')], default='branch', max_length=10, verbose_name='نوع التسليم')),
                ('delivery_address', models.TextField(blank=True, null=True, verbose_name='عنوان التسليم')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الطلب')),
                ('order_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الطلب')),
                ('status', models.CharField(choices=[('normal', 'عادي'), ('vip', 'VIP')], default='normal', max_length=20, verbose_name='حالة الطلب')),
                ('selected_types', models.JSONField(default=list, verbose_name='أنواع الطلب المختارة')),
                ('order_type', models.CharField(blank=True, choices=[('product', 'منتج'), ('service', 'خدمة')], max_length=10, null=True)),
                ('service_types', models.JSONField(blank=True, default=list, verbose_name='أنواع الخدمات')),
                ('tracking_status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('processing', 'قيد المعالجة'), ('warehouse', 'في المستودع'), ('factory', 'في المصنع'), ('cutting', 'قيد القص'), ('ready', 'جاهز للتسليم'), ('delivered', 'تم التسليم')], default='pending', max_length=20, verbose_name='حالة التتبع')),
                ('last_notification_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ آخر إشعار')),
                ('invoice_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الفاتورة')),
                ('contract_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم العقد')),
                ('payment_verified', models.BooleanField(default=False, verbose_name='تم التحقق من السداد')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المبلغ الإجمالي')),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المبلغ المدفوع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('final_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='السعر النهائي')),
                ('modified_at', models.DateTimeField(auto_now=True, help_text='آخر تحديث للطلب')),
                ('branch', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='accounts.branch', verbose_name='الفرع')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_orders', to='customers.customer', verbose_name='العميل')),
                ('salesperson', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='orders', to='accounts.salesperson', verbose_name='البائع')),
            ],
            options={
                'verbose_name': 'طلب',
                'verbose_name_plural': 'الطلبات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FabricOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الكمية')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('cutting', 'قيد التقطيع'), ('completed', 'مكتمل'), ('cancelled', 'ملغي')], default='pending', max_length=10, verbose_name='الحالة')),
                ('sent_to_warehouse', models.BooleanField(default=False, verbose_name='تم إرسال للمخزن')),
                ('cutting_completed', models.BooleanField(default=False, verbose_name='تم التقطيع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('extended_order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='fabric_order', to='orders.extendedorder', verbose_name='الطلب')),
                ('fabric_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='fabric_orders', to='inventory.product', verbose_name='نوع القماش')),
            ],
            options={
                'verbose_name': 'طلب قماش',
                'verbose_name_plural': 'طلبات الأقمشة',
            },
        ),
        migrations.AddField(
            model_name='extendedorder',
            name='order',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='extended_info', to='orders.order', verbose_name='الطلب'),
        ),
        migrations.CreateModel(
            name='AccessoryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('extended_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='accessory_items', to='orders.extendedorder', verbose_name='الطلب')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='accessory_items', to='inventory.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر اكسسوار',
                'verbose_name_plural': 'عناصر الاكسسوارات',
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('payment_method', models.CharField(choices=[('cash', 'نقداً'), ('bank_transfer', 'تحويل بنكي'), ('check', 'شيك')], default='cash', max_length=20, verbose_name='طريقة الدفع')),
                ('payment_date', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الدفع')),
                ('reference_number', models.CharField(blank=True, max_length=100, verbose_name='رقم المرجع')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='orders.order', verbose_name='الطلب')),
            ],
            options={
                'verbose_name': 'دفعة',
                'verbose_name_plural': 'الدفعات',
                'ordering': ['-payment_date'],
                'indexes': [models.Index(fields=['order'], name='payment_order_idx'), models.Index(fields=['payment_method'], name='payment_method_idx'), models.Index(fields=['payment_date'], name='payment_date_idx'), models.Index(fields=['created_by'], name='payment_created_by_idx')],
            },
        ),
        migrations.CreateModel(
            name='OrderStatusLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('processing', 'قيد المعالجة'), ('warehouse', 'في المستودع'), ('factory', 'في المصنع'), ('cutting', 'قيد القص'), ('ready', 'جاهز للتسليم'), ('delivered', 'تم التسليم')], max_length=20, verbose_name='الحالة السابقة')),
                ('new_status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('processing', 'قيد المعالجة'), ('warehouse', 'في المستودع'), ('factory', 'في المصنع'), ('cutting', 'قيد القص'), ('ready', 'جاهز للتسليم'), ('delivered', 'تم التسليم')], max_length=20, verbose_name='الحالة الجديدة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التغيير')),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التغيير بواسطة')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_logs', to='orders.order', verbose_name='الطلب')),
            ],
            options={
                'verbose_name': 'سجل حالة الطلب',
                'verbose_name_plural': 'سجلات حالة الطلب',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['order'], name='status_log_order_idx'), models.Index(fields=['created_at'], name='status_log_date_idx')],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='سعر الوحدة')),
                ('item_type', models.CharField(choices=[('fabric', 'قماش'), ('accessory', 'إكسسوار')], default='fabric', max_length=10, verbose_name='نوع العنصر')),
                ('processing_status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('processing', 'قيد المعالجة'), ('warehouse', 'في المستودع'), ('factory', 'في المصنع'), ('cutting', 'قيد القص'), ('ready', 'جاهز للتسليم'), ('delivered', 'تم التسليم')], default='pending', max_length=20, verbose_name='حالة المعالجة')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='orders.order', verbose_name='الطلب')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='order_items', to='inventory.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'عنصر الطلب',
                'verbose_name_plural': 'عناصر الطلب',
                'indexes': [models.Index(fields=['order'], name='order_item_order_idx'), models.Index(fields=['product'], name='order_item_product_idx'), models.Index(fields=['processing_status'], name='order_item_status_idx'), models.Index(fields=['item_type'], name='order_item_type_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['customer'], name='order_customer_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['salesperson'], name='order_salesperson_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['tracking_status'], name='order_tracking_status_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['order_date'], name='order_date_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['branch', 'tracking_status'], name='order_branch_status_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['payment_verified'], name='order_payment_verified_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['created_at'], name='order_created_at_idx'),
        ),
    ]
