# Generated by Django 4.2.21 on 2025-07-02 13:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0003_order_expected_delivery_date_alter_order_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='order',
            name='contract_google_drive_file_id',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='معرف ملف العقد في Google Drive'),
        ),
        migrations.AddField(
            model_name='order',
            name='contract_google_drive_file_name',
            field=models.CharField(blank=True, max_length=500, null=True, verbose_name='اسم ملف العقد في Google Drive'),
        ),
        migrations.AddField(
            model_name='order',
            name='contract_google_drive_file_url',
            field=models.URLField(blank=True, null=True, verbose_name='رابط ملف العقد في Google Drive'),
        ),
        migrations.AddField(
            model_name='order',
            name='is_contract_uploaded_to_drive',
            field=models.BooleanField(default=False, verbose_name='تم رفع العقد إلى Google Drive'),
        ),
    ]
