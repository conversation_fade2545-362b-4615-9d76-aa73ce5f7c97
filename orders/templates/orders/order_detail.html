{% extends 'base.html' %}

{% block title %}تفاصيل الطلب - نظام الخواجه{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-shopping-cart"></i> تفاصيل الطلب #{{ order.order_number }}</h2>
        <div>
            <a href="{% url 'orders:order_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-right"></i> العودة للقائمة
            </a>
            <a href="{% url 'orders:order_update' order.pk %}" class="btn btn-primary">
                <i class="fas fa-edit"></i> تعديل الطلب
            </a>
            <a href="{% url 'orders:order_delete' order.pk %}" class="btn btn-danger">
                <i class="fas fa-trash"></i> حذف الطلب
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Order Details -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">معلومات الطلب</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">رقم الطلب</th>
                            <td>{{ order.order_number }}</td>
                        </tr>
                        <tr>
                            <th>العميل</th>
                            <td>
                                <a href="{% url 'customers:customer_detail' order.customer.pk %}">
                                    {{ order.customer.name }}
                                </a>
                            </td>
                        </tr>
                        <tr>
                            <th>البائع</th>
                            <td>{% if order.salesperson %}{{ order.salesperson }}{% else %}-{% endif %}</td>
                        </tr>
                        <tr>
                            <th>تاريخ الطلب</th>
                            <td>{{ order.order_date|date:"Y-m-d H:i" }}</td>
                        </tr>
                        <!-- Show different date info for inspection vs other orders -->
                        {% if 'inspection' in order.get_selected_types_list or 'inspection' in order.selected_types %}
                            <!-- For inspection orders, show status and dates -->
                            {% with inspection=order.inspections.first %}
                                {% if inspection %}
                                    {% if inspection.status == 'completed' %}
                                    <tr>
                                        <th>حالة المعاينة</th>
                                        <td>
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>
                                                مكتملة
                                            </span>
                                            {% if inspection.updated_at %}
                                                <br><small class="text-muted">
                                                    آخر تحديث: {{ inspection.updated_at|date:"Y-m-d H:i" }}
                                                </small>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% elif inspection.scheduled_date %}
                                    <tr>
                                        <th>تاريخ المعاينة المجدولة</th>
                                        <td>
                                            <span class="badge bg-warning text-dark">
                                                <i class="fas fa-calendar me-1"></i>
                                                {{ inspection.scheduled_date|date:"Y-m-d" }}
                                            </span>
                                            <br><small class="text-muted">
                                                الحالة: {{ inspection.get_status_display }}
                                            </small>
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <th>حالة المعاينة</th>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-hourglass-half me-1"></i>
                                                في انتظار الجدولة
                                            </span>
                                            <br><small class="text-muted">
                                                تاريخ الطلب: {{ inspection.request_date|date:"Y-m-d" }}
                                            </small>
                                        </td>
                                    </tr>
                                    {% endif %}
                                {% else %}
                                <tr>
                                    <th>حالة المعاينة</th>
                                    <td>
                                        <span class="badge bg-danger">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            لم يتم إنشاء المعاينة بعد
                                        </span>
                                    </td>
                                </tr>
                                {% endif %}
                            {% endwith %}
                        {% else %}
                            <!-- For other orders, show expected delivery date -->
                            {% if order.expected_delivery_date %}
                            <tr>
                                <th>تاريخ التسليم المتوقع</th>
                                <td>
                                    <span class="badge bg-info">
                                        {{ order.expected_delivery_date|date:"Y-m-d" }}
                                    </span>
                                    {% if order.status == 'vip' %}
                                        <small class="text-warning ms-2">
                                            <i class="fas fa-star"></i> VIP - أولوية عالية
                                        </small>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endif %}
                            <tr>
                                <th>تاريخ التسليم</th>
                                <td>{{ order.delivery_date|date:"Y-m-d"|default:"غير محدد" }}</td>
                            </tr>
                        {% endif %}
                        <tr>
                            <th>حالة الطلب</th>
                            <td>
                                {% if order.status == 'pending' %}
                                <span class="badge bg-warning">قيد الانتظار</span>
                                {% elif order.status == 'processing' %}
                                <span class="badge bg-info">قيد التنفيذ</span>
                                {% elif order.status == 'completed' %}
                                <span class="badge bg-success">مكتمل</span>
                                {% elif order.status == 'cancelled' %}
                                <span class="badge bg-danger">ملغي</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>المبلغ الإجمالي</th>
                            <td>{{ order.total_amount }} {{ currency_symbol }}</td>
                        </tr>
                        <tr>
                            <th>المبلغ المدفوع</th>
                            <td>{{ order.paid_amount }} {{ currency_symbol }}</td>
                        </tr>
                        <tr>
                            <th>المبلغ المتبقي</th>
                            <td>{{ order.remaining_amount }} {{ currency_symbol }}</td>
                        </tr>
                        <tr>
                            <th>تم الإنشاء بواسطة</th>
                            <td>
                                {% if order.created_by %}
                                    {{ order.created_by.get_full_name|default:order.created_by.username }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>تاريخ الإنشاء</th>
                            <td>{{ order.created_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                        <tr>
                            <th>تاريخ التحديث</th>
                            <td>{{ order.updated_at|date:"Y-m-d H:i" }}</td>
                        </tr>

                    </table>
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">معلومات العميل</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">الاسم</th>
                            <td>{{ order.customer.name }}</td>
                        </tr>
                        <tr>
                            <th>رقم الهاتف</th>
                            <td>{{ order.customer.phone }}</td>
                        </tr>
                        <tr>
                            <th>البريد الإلكتروني</th>
                            <td>{{ order.customer.email|default:"غير متوفر" }}</td>
                        </tr>
                        <tr>
                            <th>العنوان</th>
                            <td>{{ order.customer.address|default:"غير متوفر" }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- Notes -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">ملاحظات</h5>
                </div>
                <div class="card-body">
                    {% if order.notes %}
                    <p class="mb-0">{{ order.notes|linebreaks }}</p>
                    {% else %}
                    <p class="text-muted mb-0">لا توجد ملاحظات</p>
                    {% endif %}
                </div>
            </div>

            <!-- Order Type Information -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">معلومات نوع الطلب</h5>
                </div>
                <div class="card-body">
                    <table class="table table-bordered">
                        <tr>
                            <th style="width: 40%">نوع الطلب</th>
                            <td>
                                {% with types_list=order.get_selected_types_list %}
                                    {% if types_list %}
                                        {% for type in types_list %}
                                            {% if type == 'accessory' %}
                                                <span class="badge bg-primary me-2">
                                                    <i class="fas fa-gem me-1"></i> إكسسوار
                                                </span>
                                            {% elif type == 'installation' %}
                                                <span class="badge bg-warning me-2">
                                                    <i class="fas fa-tools me-1"></i> تركيب
                                                </span>
                                            {% elif type == 'inspection' %}
                                                <span class="badge bg-info me-2">
                                                    <i class="fas fa-eye me-1"></i> معاينة
                                                </span>
                                            {% elif type == 'tailoring' %}
                                                <span class="badge bg-success me-2">
                                                    <i class="fas fa-cut me-1"></i> تفصيل
                                                </span>
                                            {% else %}
                                                <span class="badge bg-secondary me-2">{{ type }}</span>
                                            {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                {% endwith %}
                            </td>
                        </tr>

                        <!-- Inspection Status for inspection orders -->
                        {% if 'inspection' in order.get_selected_types_list %}
                        <tr>
                            <th>حالة المعاينة</th>
                            <td>
                                {% with inspection=order.inspections.first %}
                                    {% if inspection %}
                                        <span class="badge {% if inspection.status == 'pending' %}bg-warning text-dark
                                                       {% elif inspection.status == 'in_progress' %}bg-info
                                                       {% elif inspection.status == 'completed' %}bg-success
                                                       {% else %}bg-danger{% endif %} me-2">
                                            {% if inspection.status == 'pending' %}
                                                <i class="fas fa-clock me-1"></i> في الانتظار
                                            {% elif inspection.status == 'in_progress' %}
                                                <i class="fas fa-spinner me-1"></i> قيد التنفيذ
                                            {% elif inspection.status == 'completed' %}
                                                <i class="fas fa-check me-1"></i> مكتملة
                                            {% else %}
                                                <i class="fas fa-times me-1"></i> ملغية
                                            {% endif %}
                                        </span>

                                        {% if inspection.result %}
                                            <span class="badge {% if inspection.result == 'passed' %}bg-success
                                                           {% else %}bg-danger{% endif %}">
                                                {% if inspection.result == 'passed' %}
                                                    <i class="fas fa-thumbs-up me-1"></i> نجحت
                                                {% else %}
                                                    <i class="fas fa-thumbs-down me-1"></i> فشلت
                                                {% endif %}
                                            </span>
                                        {% endif %}

                                        <div class="mt-2">
                                            <small class="text-muted">
                                                آخر تحديث: {{ inspection.updated_at|date:"Y-m-d H:i" }}
                                            </small>
                                        </div>
                                    {% else %}
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-exclamation-triangle me-1"></i> لم يتم إنشاء المعاينة بعد
                                        </span>
                                    {% endif %}
                                {% endwith %}
                            </td>
                        </tr>
                        {% endif %}

                        {% if order.goods_type %}
                        <tr>
                            <th>نوع البضاعة</th>
                            <td>{{ order.get_goods_type_display }}</td>
                        </tr>
                        {% endif %}
                        {% if order.service_types %}
                        <tr>
                            <th>أنواع الخدمات</th>
                            <td>
                                {% for service_type in order.service_types %}
                                    {% if service_type == 'installation' %}
                                        <span class="badge bg-info">تركيب</span>
                                    {% elif service_type == 'inspection' %}
                                        <span class="badge bg-primary">معاينة</span>
                                    {% elif service_type == 'transport' %}
                                        <span class="badge bg-secondary">نقل</span>
                                    {% elif service_type == 'tailoring' %}
                                        <span class="badge bg-warning">تفصيل</span>
                                    {% endif %}
                                {% endfor %}
                            </td>
                        </tr>
                        {% endif %}
                        {% if order.invoice_number %}
                        <tr>
                            <th>رقم الفاتورة</th>
                            <td>{{ order.invoice_number }}</td>
                        </tr>
                        {% endif %}
                        {% if order.contract_number %}
                        <tr>
                            <th>رقم العقد</th>
                            <td>{{ order.contract_number }}</td>
                        </tr>
                        {% endif %}
                        <tr>
                            <th>تم التحقق من الدفع</th>
                            <td>
                                {% if order.payment_verified %}
                                    <span class="badge bg-success">نعم</span>
                                {% else %}
                                    <span class="badge bg-danger">لا</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>الفرع</th>
                            <td>{{ order.branch.name }}</td>
                        </tr>
                        {% if 'inspection' in order.service_types and order.inspections.exists %}
                        <tr>
                            <th>ملف المعاينة</th>
                            <td>
                                {% for inspection in order.inspections.all %}
                                    {% if inspection.is_uploaded_to_drive and inspection.google_drive_file_url %}
                                        <a href="{{ inspection.google_drive_file_url }}" target="_blank" title="عرض ملف المعاينة {{ forloop.counter }} في Google Drive">
                                            <i class="fas fa-file-pdf text-danger" style="font-size: 20px;"></i>
                                        </a>
                                    {% elif inspection.inspection_file %}
                                        <span title="جاري رفع ملف المعاينة {{ forloop.counter }} إلى Google Drive">
                                            <i class="fas fa-file-pdf text-warning" style="font-size: 20px;"></i>
                                            <i class="fas fa-clock text-warning" style="font-size: 10px; position: relative; top: -8px; left: -3px;"></i>
                                        </span>
                                    {% endif %}
                                    {% if not forloop.last %}<br>{% endif %}
                                {% endfor %}
                            </td>
                        </tr>
                        {% endif %}
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج تحديث الحالة - Hidden for inspection orders -->
    {% if not 'inspection' in order.get_selected_types_list and not 'inspection' in order.selected_types %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">تحديث حالة الطلب</h5>
        </div>
        <div class="card-body">
            <form method="post" action="{% url 'orders:update_status' order.id %}">
                {% csrf_token %}
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة الجديدة*</label>
                            <select name="status" id="status" class="form-select" required>
                                {% for value, label in order.TRACKING_STATUS_CHOICES %}
                                    <option value="{{ value }}" {% if value == order.tracking_status %}selected{% endif %}>
                                        {{ label }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="notes" class="form-label">ملاحظات</label>
                            <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                        </div>
                    </div>
                </div>
                <div class="text-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> تحديث الحالة
                    </button>
                </div>
            </form>
        </div>
    </div>
    {% endif %}

    <!-- سجل تغييرات الحالة - Hidden for inspection orders -->
    {% if not 'inspection' in order.get_selected_types_list and not 'inspection' in order.selected_types %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">سجل تغييرات الحالة</h5>
        </div>
        <div class="card-body">
            <div class="timeline">
                {% for log in order.status_logs.all %}
                <div class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                        <h6 class="mb-0">{{ log.get_new_status_display }}</h6>
                        <small class="text-muted">
                            {{ log.created_at|date:"Y-m-d H:i" }} -
                            بواسطة {{ log.changed_by.get_full_name|default:log.changed_by.username }}
                        </small>
                        {% if log.notes %}
                        <p class="mb-0 mt-2">{{ log.notes }}</p>
                        {% endif %}
                    </div>
                </div>
                {% empty %}
                <p class="text-muted">لا يوجد سجل لتغييرات الحالة</p>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Order Items - Hidden for inspection orders -->
    {% if 'inspection' not in order.get_selected_types_list %}
    <div class="card mb-4">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0">عناصر الطلب</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>سعر الوحدة</th>
                            <th>السعر الإجمالي</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for item in order_items %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>{{ item.product.name }}</td>
                            <td>{{ item.quantity }}</td>
                            <td>{{ item.unit_price }} {{ currency_symbol }}</td>
                            <td>{{ item.total_price }} {{ currency_symbol }}</td>
                            <td>{{ item.notes|default:"-" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center py-3">لا توجد عناصر في هذا الطلب</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="table-light">
                            <th colspan="4" class="text-start">المجموع</th>
                            <th>{{ order.total_amount }} {{ currency_symbol }}</th>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    {% if 'inspection' in order.get_selected_types_list or 'inspection' in order.selected_types %}
    <!-- Inspection Details -->
    <div class="card mb-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="fas fa-eye me-2"></i>
                بطاقة تتبع المعاينة
            </h5>
            <small>معلومات شاملة عن حالة المعاينة المرتبطة بهذا الطلب</small>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead class="table-info">
                        <tr>
                            <th width="8%">رقم المعاينة</th>
                            <th width="12%">تاريخ الطلب</th>
                            <th width="12%">تاريخ التنفيذ</th>
                            <th width="8%">عدد الشبابيك</th>
                            <th width="15%">فني المعاينة</th>
                            <th width="10%">الحالة</th>
                            <th width="10%">النتيجة</th>
                            <th width="8%">ملف المعاينة</th>
                            <th width="12%">البائع المسؤول</th>
                            <th width="5%">عرض</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for inspection in inspections %}
                        <tr class="{% if inspection.status == 'completed' %}table-success{% elif inspection.status == 'in_progress' %}table-info{% elif inspection.status == 'pending' %}table-warning{% endif %}">
                            <td>
                                <strong class="text-primary">#{{ inspection.id }}</strong>
                            </td>
                            <td>
                                <small class="text-muted">{{ inspection.request_date|date:"Y-m-d" }}</small><br>
                                <small class="text-muted">{{ inspection.request_date|date:"H:i" }}</small>
                            </td>
                            <td>
                                {% if inspection.scheduled_date %}
                                    <small class="text-muted">{{ inspection.scheduled_date|date:"Y-m-d" }}</small><br>
                                    <small class="text-muted">{{ inspection.scheduled_date|date:"H:i" }}</small>
                                {% else %}
                                    <span class="text-muted">غير محدد</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if inspection.windows_count %}
                                    <span class="badge bg-secondary">{{ inspection.windows_count }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if inspection.inspector %}
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user-hard-hat me-2 text-primary"></i>
                                        <div>
                                            <div class="fw-bold">
                                                {% if inspection.inspector.get_full_name %}
                                                    {{ inspection.inspector.get_full_name }}
                                                {% else %}
                                                    {{ inspection.inspector.username }}
                                                {% endif %}
                                            </div>
                                            <small class="text-muted">فني معاينة</small>
                                        </div>
                                    </div>
                                {% else %}
                                    <span class="text-muted">
                                        <i class="fas fa-user-slash me-1"></i>
                                        غير محدد
                                    </span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="badge {% if inspection.status == 'pending' %}bg-warning text-dark
                                           {% elif inspection.status == 'in_progress' %}bg-info
                                           {% elif inspection.status == 'completed' %}bg-success
                                           {% else %}bg-danger{% endif %} px-3 py-2">
                                    {% if inspection.status == 'pending' %}
                                        <i class="fas fa-clock me-1"></i> في الانتظار
                                    {% elif inspection.status == 'in_progress' %}
                                        <i class="fas fa-spinner me-1"></i> قيد التنفيذ
                                    {% elif inspection.status == 'completed' %}
                                        <i class="fas fa-check me-1"></i> مكتملة
                                    {% else %}
                                        <i class="fas fa-times me-1"></i> ملغية
                                    {% endif %}
                                </span>
                                {% if inspection.status == 'completed' and inspection.updated_at %}
                                    <br><small class="text-muted mt-1">{{ inspection.updated_at|date:"Y-m-d H:i" }}</small>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if inspection.result %}
                                    <span class="badge {% if inspection.result == 'passed' %}bg-success
                                               {% else %}bg-danger{% endif %} px-3 py-2">
                                        {% if inspection.result == 'passed' %}
                                            <i class="fas fa-thumbs-up me-1"></i> نجحت
                                        {% else %}
                                            <i class="fas fa-thumbs-down me-1"></i> فشلت
                                        {% endif %}
                                    </span>
                                {% else %}
                                    <span class="text-muted">
                                        <i class="fas fa-hourglass-half me-1"></i>
                                        في الانتظار
                                    </span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if inspection.is_uploaded_to_drive and inspection.google_drive_file_url %}
                                    <a href="{{ inspection.google_drive_file_url }}" target="_blank"
                                       class="btn btn-sm btn-outline-danger"
                                       title="عرض ملف المعاينة في Google Drive">
                                        <i class="fas fa-file-pdf me-1"></i>
                                        عرض الملف
                                    </a>
                                {% elif inspection.inspection_file %}
                                    <span class="badge bg-warning text-dark" title="جاري رفع الملف إلى Google Drive">
                                        <i class="fas fa-clock me-1"></i>
                                        جاري الرفع
                                    </span>
                                {% else %}
                                    <span class="text-muted">
                                        <i class="fas fa-file-slash me-1"></i>
                                        لا يوجد ملف
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                {% if inspection.responsible_employee %}
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user-tie me-2 text-success"></i>
                                        <div>
                                            <div class="fw-bold">{{ inspection.responsible_employee }}</div>
                                            <small class="text-muted">بائع مسؤول</small>
                                        </div>
                                    </div>
                                {% else %}
                                    <span class="text-muted">
                                        <i class="fas fa-user-slash me-1"></i>
                                        غير محدد
                                    </span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <a href="{% url 'inspections:inspection_detail' inspection.pk %}"
                                   class="btn btn-sm btn-outline-info"
                                   title="عرض تفاصيل المعاينة">
                                    <i class="fas fa-eye me-1"></i>
                                    عرض
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="10" class="text-center py-3">لم يتم إنشاء معاينة لهذا الطلب بعد</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Payments -->
    <div class="card">
        <div class="card-header bg-light d-flex justify-content-between align-items-center">
            <h5 class="mb-0">الدفعات</h5>
            <a href="{% url 'orders:payment_create' order.pk %}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> تسجيل دفعة
            </a>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>المبلغ</th>
                            <th>طريقة الدفع</th>
                            <th>تاريخ الدفع</th>
                            <th>رقم المرجع</th>
                            <th>ملاحظات</th>
                            <th>تم الإنشاء بواسطة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>{{ payment.amount }} {{ currency_symbol }}</td>
                            <td>{{ payment.get_payment_method_display }}</td>
                            <td>{{ payment.payment_date|date:"Y-m-d H:i" }}</td>
                            <td>{{ payment.reference_number|default:"-" }}</td>
                            <td>{{ payment.notes|default:"-" }}</td>
                            <td>
                                {% if payment.created_by %}
                                    {{ payment.created_by.get_full_name|default:payment.created_by.username }}
                                {% else %}
                                    غير محدد
                                {% endif %}
                            </td>
                            <td>
                                <a href="{% url 'orders:payment_delete' payment.pk %}" class="btn btn-sm btn-danger">
                                    <i class="fas fa-trash"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center py-3">لا توجد دفعات مسجلة لهذا الطلب</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                    <tfoot>
                        <tr class="table-light">
                            <th colspan="7" class="text-start">المجموع المدفوع</th>
                            <th>{{ order.paid_amount }} {{ currency_symbol }}</th>
                        </tr>
                        <tr class="table-light">
                            <th colspan="7" class="text-start">المبلغ المتبقي</th>
                            <th>{{ order.remaining_amount }} {{ currency_symbol }}</th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
/* Timeline styling */
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline-item {
    position: relative;
    padding-left: 40px;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: 0;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #007bff;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #007bff;
}

.timeline-item:not(:last-child):before {
    content: '';
    position: absolute;
    left: 5px;
    top: 12px;
    height: calc(100% + 8px);
    width: 2px;
    background-color: #e9ecef;
}

.timeline-content {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}
</style>
{% endblock %}
