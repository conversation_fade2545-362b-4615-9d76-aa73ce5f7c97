{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }} - نظام الخواجه{% endblock %}

{% block extra_css %}
<style>
    .notes-field {
        min-height: 150px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-shopping-cart"></i> {{ title }}</h2>
        <a href="{% url 'orders:order_list' %}" class="btn btn-secondary">
            <i class="fas fa-arrow-right"></i> العودة للقائمة
        </a>
    </div>

    <form method="post" id="orderForm" novalidate>
    {{ formset.management_form }}
    <div style="display:none;">
        {% for form in formset %}
            {{ form.as_p }}
        {% endfor %}
    </div>
        {% csrf_token %}
        
        <!-- Display form errors -->
        {% if form.errors or formset.errors %}
            <div class="alert alert-danger mb-4">
                <h5 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> يوجد أخطاء في النموذج</h5>
                {% if form.non_field_errors %}
                    <ul class="mb-0">
                        {% for error in form.non_field_errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <p class="mb-0">يرجى التحقق من الحقول المطلوبة أدناه.</p>
                {% endif %}
                {% if formset.errors %}
                    <ul class="mb-0">
                    {% for error in formset.non_form_errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                    {% for f in formset.forms %}
                        {% for field in f.visible_fields %}
                            {% for error in field.errors %}
                                <li>{{ field.label }}: {{ error }}</li>
                            {% endfor %}
                        {% endfor %}
                    {% endfor %}
                    </ul>
                {% endif %}
            </div>
        {% endif %}
        
        <!-- Hidden fields for form submission -->
        <input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
        <input type="hidden" name="selected_types_hidden" id="selected_types_hidden" value="">
        <input type="hidden" name="selected_products" id="selected_products" value="">
        <input type="hidden" name="tracking_status" value="pending">
        
        <div class="row">
            <!-- Order Information -->
            <div class="col-md-12">
                <div class="card mb-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">معلومات الطلب</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <!-- Customer Selection -->
                                <div class="mb-3">
                                    <label for="{{ form.customer.id_for_label }}" class="form-label">العميل *</label>
                                    {{ form.customer }}
                                    {% if form.customer.errors %}
                                    <div class="alert alert-danger mt-2">
                                        {{ form.customer.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Order Number (Auto-generated but visible) -->
                                <div class="mb-3">
                                    <label for="{{ form.order_number.id_for_label }}" class="form-label">رقم الطلب</label>
                                    {{ form.order_number }}
                                    {% if last_order %}
                                    <div class="form-text">
                                        آخر رقم طلب للعميل: {{ last_order.order_number }}
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Order Status - Hidden for inspection -->
                                <div class="mb-3 order-status-field">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">وضع الطلب *</label>
                                    {{ form.status }}
                                    <div class="form-text text-muted">
                                        عادي: تسليم خلال 15 يوم | VIP: تسليم خلال 7 أيام
                                    </div>
                                    {% if form.status.errors %}
                                    <div class="alert alert-danger mt-2">
                                        {{ form.status.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Expected Delivery Date - Hidden -->
                                <div class="mb-3" style="display: none;">
                                    <label for="{{ form.expected_delivery_date.id_for_label }}" class="form-label">تاريخ التسليم المتوقع</label>
                                    {{ form.expected_delivery_date }}
                                    <div class="form-text text-muted">
                                        يتم حسابه تلقائياً بناءً على وضع الطلب
                                    </div>
                                    {% if form.expected_delivery_date.errors %}
                                    <div class="alert alert-danger mt-2">
                                        {{ form.expected_delivery_date.errors }}
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Invoice Number -->
                                <div class="mb-3">
                                    <label for="{{ form.invoice_number.id_for_label }}" class="form-label">
                                        رقم الفاتورة
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.invoice_number }}
                                    <div class="form-text text-danger" id="invoice_help">
                                        مطلوب لجميع أنواع الطلبات
                                    </div>
                                    {% if form.invoice_number.errors %}
                                    <div class="alert alert-danger mt-2">
                                        {% for error in form.invoice_number.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Salesperson -->
                                <div class="mb-3">
                                    <label for="{{ form.salesperson.id_for_label }}" class="form-label">
                                        البائع
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.salesperson }}
                                    {% if form.salesperson.errors %}
                                    <div class="alert alert-danger mt-2">
                                        {% for error in form.salesperson.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>

                            </div>
                            
                            <div class="col-md-6">
                                <!-- Order Types -->
                                <div class="mb-4 {% if form.selected_types.errors %}border border-danger rounded p-3{% endif %}">
                                    <h5 class="mb-3">نوع الطلب *</h5>
                                    {% if form.selected_types.errors %}
                                    <div class="alert alert-danger">
                                        {% for error in form.selected_types.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <div class="d-flex flex-wrap gap-3 mb-3">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="selected_types" id="order_type_accessory" value="accessory">
                                            <label class="form-check-label" for="order_type_accessory">
                                                <i class="fas fa-gem me-2"></i> إكسسوار
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="selected_types" id="order_type_installation" value="installation">
                                            <label class="form-check-label" for="order_type_installation">
                                                <i class="fas fa-tools me-2"></i> تركيب
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="selected_types" id="order_type_inspection" value="inspection">
                                            <label class="form-check-label" for="order_type_inspection">
                                                <i class="fas fa-eye me-2"></i> معاينة
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="selected_types" id="order_type_tailoring" value="tailoring">
                                            <label class="form-check-label" for="order_type_tailoring">
                                                <i class="fas fa-cut me-2"></i> تفصيل
                                            </label>
                                        </div>
                                    </div>
                                    <div class="form-text text-muted">
                                        <strong>ملاحظة:</strong> يجب اختيار نوع واحد فقط للطلب
                                    </div>
                                </div>
                                
                                <!-- Contract Number -->
                                <div class="mb-3 contract-field" style="display: none;">
                                    <label for="{{ form.contract_number.id_for_label }}" class="form-label">
                                        رقم العقد
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.contract_number }}
                                    <div class="form-text text-danger">
                                        مطلوب عند اختيار التركيب، التفصيل، أو الإكسسوار
                                    </div>
                                    {% if form.contract_number.errors %}
                                    <div class="alert alert-danger mt-2">
                                        {% for error in form.contract_number.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Contract File -->
                                <div class="mb-3 contract-file-field" style="display: none;">
                                    <label for="{{ form.contract_file.id_for_label }}" class="form-label">
                                        ملف العقد (PDF)
                                        <span class="text-danger">*</span>
                                    </label>
                                    {{ form.contract_file }}
                                    <div class="form-text text-muted">
                                        يجب أن يكون الملف من نوع PDF وأقل من 10 ميجابايت
                                        <br><strong>سيتم رفع الملف تلقائياً إلى Google Drive عند الحفظ</strong>
                                    </div>
                                    {% if form.contract_file.errors %}
                                    <div class="alert alert-danger mt-2">
                                        {% for error in form.contract_file.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}

                                    <!-- Google Drive Status -->
                                    {% if form.instance.is_contract_uploaded_to_drive %}
                                    <div class="alert alert-success mt-2">
                                        <i class="fas fa-cloud-upload-alt me-2"></i>
                                        تم رفع الملف إلى Google Drive بنجاح
                                        {% if form.instance.contract_google_drive_file_url %}
                                        <br>
                                        <a href="{{ form.instance.contract_google_drive_file_url }}" target="_blank" class="btn btn-sm btn-outline-primary mt-2">
                                            <i class="fas fa-external-link-alt me-1"></i>
                                            عرض الملف في Google Drive
                                        </a>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Delivery Options - Hidden, will be set automatically -->
                                <div class="mb-4" style="display: none;">
                                    <h5 class="mb-3">خيارات التسليم *</h5>
                                    <div class="d-flex flex-wrap gap-3 mb-3">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="delivery_option" id="delivery_option_home" value="home">
                                            <label class="form-check-label" for="delivery_option_home">
                                                <i class="fas fa-home me-2"></i> توصيل للمنزل
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="delivery_option" id="delivery_option_branch" value="branch">
                                            <label class="form-check-label" for="delivery_option_branch">
                                                <i class="fas fa-store me-2"></i> استلام من الفرع
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Customer Address (for home delivery) - Hidden -->
                                <div class="mb-3 customer-address-field" style="display: none;">
                                    <label class="form-label">عنوان التوصيل *</label>
                                    <textarea name="delivery_address" class="form-control" rows="2"></textarea>
                                </div>

                                <!-- Branch Selection (for branch pickup) - Hidden -->
                                <div class="mb-3 branch-selection-field" style="display: none;">
                                    <label for="{{ form.branch.id_for_label }}" class="form-label">الفرع *</label>
                                    {{ form.branch }}
                                    {% if form.branch.errors %}
                                    <div class="alert alert-danger mt-2">
                                        {{ form.branch.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Notes -->
                                <div class="mb-4">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">ملاحظات</label>
                                    <textarea name="notes" id="{{ form.notes.id_for_label }}" class="form-control notes-field" rows="6">{{ form.notes.value|default:'' }}</textarea>
                                    <div class="form-text">
                                        يمكنك إضافة معلومات العميل وتفاصيل الطلب هنا
                                    </div>
                                    <!-- Debug Information -->
                                    {% if debug %}
                                    <div class="alert alert-info mt-2">
                                        <strong>معلومات التصحيح:</strong><br>
                                        {{ form.errors }}<br>
                                        {{ form.non_field_errors }}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Product Selection Section -->
        <div class="card mb-4 product-selection-section" id="product-selection-section" style="display: none;">
            <div class="card-header bg-light">
                <h5 class="mb-0"><i class="fas fa-box-open me-2"></i> اختيار المنتجات</h5>
            </div>
            <div class="card-body">
                <!-- Product Search -->
                <div class="mb-4">
                    <div class="input-group">
                        <input type="text" id="product-search" class="form-control" placeholder="ابحث عن منتج بالاسم أو الكود..." aria-label="ابحث عن منتج">
                        <button class="btn btn-outline-primary" type="button" id="product-search-button">
                            <i class="fas fa-search"></i> بحث
                        </button>
                    </div>
                </div>
                
                <div id="products-container" class="row">
                    <!-- Products will be loaded here -->
                    <div class="text-center py-3" id="products-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحميل المنتجات...</p>
                    </div>
                </div>
                
                <!-- Selected Products -->
                <div class="mb-4">
                    <h5 class="mb-3">المنتجات المختارة</h5>
                    <div id="selected-products-container">
                        <p class="text-muted" id="no-products-message">لم يتم اختيار أي منتجات بعد</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save me-2"></i> حفظ الطلب
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get DOM elements
        const orderForm = document.getElementById('orderForm');
        const productSelectionSection = document.getElementById('product-selection-section');
        const contractField = document.querySelector('.contract-field');
        const contractFileField = document.querySelector('.contract-file-field');
        const customerAddressField = document.querySelector('.customer-address-field');
        const branchSelectionField = document.querySelector('.branch-selection-field');
        const orderStatusField = document.querySelector('.order-status-field');
        const customerSelect = document.getElementById('{{ form.customer.id_for_label }}');
        
        // Add event listener for customer select
        if (customerSelect) {
            customerSelect.addEventListener('change', function() {
                const customerId = this.value;
                if (customerId) {
                    // Redirect to the same page with customer_id parameter
                    window.location.href = `?customer_id=${customerId}`;
                }
            });
        }
        
        // Delivery option radio buttons
        const deliveryOptionHome = document.getElementById('delivery_option_home');
        const deliveryOptionBranch = document.getElementById('delivery_option_branch');
        
        // Add event listeners for all order type checkboxes
        document.querySelectorAll('input[name="selected_types"]').forEach(checkbox => {
            checkbox.addEventListener('change', handleOrderTypeChange);
        });

        // Add event listener for status change to update expected delivery date
        const statusSelect = document.getElementById('{{ form.status.id_for_label }}');
        if (statusSelect) {
            statusSelect.addEventListener('change', updateExpectedDeliveryDate);
        }
        
        if (deliveryOptionHome) {
            deliveryOptionHome.addEventListener('change', handleDeliveryOptionChange);
        }
        
        if (deliveryOptionBranch) {
            deliveryOptionBranch.addEventListener('change', handleDeliveryOptionChange);
        }
        
        // Add form submit event listener
        if (orderForm) {
            orderForm.addEventListener('submit', function(e) {
                // Prevent default form submission
                e.preventDefault();
                
                // Validate form
                if (!validateForm()) {
                    // Show error message
                    const errorMessage = document.createElement('div');
                    errorMessage.className = 'alert alert-danger mb-4';
                    errorMessage.innerHTML = '<h5 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> يوجد أخطاء في النموذج</h5><p class="mb-0">يرجى التحقق من الحقول المطلوبة أدناه.</p>';
                    
                    // Insert at the top of the form
                    orderForm.insertBefore(errorMessage, orderForm.firstChild);
                    
                    // Scroll to top of form
                    window.scrollTo({
                        top: orderForm.offsetTop - 100,
                        behavior: 'smooth'
                    });
                    
                    return;
                }
                
                // Update hidden fields before submitting
            const selectedOrderTypes = Array.from(document.querySelectorAll('input[name="selected_types"]:checked'));
                document.getElementById('selected_types_hidden').value = JSON.stringify(selectedOrderTypes.map(type => type.value));
                
                // Update selected products input if accessory is selected
                if (selectedOrderType && selectedOrderType.value === 'accessory') {
                    updateSelectedProductsInput();
                }
                
                // Submit the form
                orderForm.submit();
            });
        }
        
        // Validate form
        function validateForm() {
            let isValid = true;
            
            // Check customer
            const customerSelect = document.getElementById('{{ form.customer.id_for_label }}');
            if (customerSelect && !customerSelect.value) {
                isValid = false;
                highlightError(customerSelect);
            }
            
            // Check order type (single selection)
            const selectedOrderType = document.querySelector('input[name="selected_types"]:checked');
            if (!selectedOrderType) {
                isValid = false;
                // Highlight order type section
                const orderTypeSection = document.querySelector('.mb-4:has([name="selected_types"])');
                if (orderTypeSection) {
                    orderTypeSection.classList.add('border', 'border-danger', 'p-2', 'rounded');
                }
            } else {
                // التحقق من رقم العقد وملف العقد للأنواع المطلوبة
                const contractRequiredTypes = ['installation', 'tailoring', 'accessory'];
                if (selectedOrderType && contractRequiredTypes.includes(selectedOrderType.value)) {
                    const contractInput = document.getElementById('{{ form.contract_number.id_for_label }}');
                    if (contractInput && !contractInput.value.trim()) {
                        isValid = false;
                        highlightError(contractInput);
                    }

                    const contractFileInput = document.getElementById('{{ form.contract_file.id_for_label }}');
                    if (contractFileInput && !contractFileInput.files.length) {
                        isValid = false;
                        highlightError(contractFileInput);
                    }
                }

                // Check invoice number - required for all types
                const invoiceInput = document.getElementById('{{ form.invoice_number.id_for_label }}');
                if (invoiceInput && !invoiceInput.value.trim()) {
                    isValid = false;
                    highlightError(invoiceInput);
                }
                
                // التحقق من اختيار المنتجات للإكسسوار فقط
                if (selectedOrderType && selectedOrderType.value === 'accessory') {
                    const selectedProducts = document.querySelectorAll('.product-checkbox:checked');
                    if (selectedProducts.length === 0) {
                        isValid = false;
                        const productSection = document.getElementById('product-selection-section');
                        if (productSection) {
                            productSection.classList.add('border', 'border-danger', 'p-2', 'rounded');
                        }
                    }
                }
            }
            
            // Check delivery option
            if (!deliveryOptionHome.checked && !deliveryOptionBranch.checked) {
                isValid = false;
                // Highlight delivery options section
                const deliveryOptionsSection = document.querySelector('.mb-4:has(#delivery_option_home)');
                if (deliveryOptionsSection) {
                    deliveryOptionsSection.classList.add('border', 'border-danger', 'p-2', 'rounded');
                }
            }
            
            // Check branch if branch pickup is selected
            if (deliveryOptionBranch && deliveryOptionBranch.checked) {
                const branchSelect = document.getElementById('{{ form.branch.id_for_label }}');
                if (branchSelect && !branchSelect.value) {
                    isValid = false;
                    highlightError(branchSelect);
                }
            }
            
            // Check delivery address if home delivery is selected
            if (deliveryOptionHome && deliveryOptionHome.checked) {
                const deliveryAddressTextarea = document.querySelector('textarea[name="delivery_address"]');
                if (deliveryAddressTextarea && !deliveryAddressTextarea.value.trim()) {
                    isValid = false;
                    highlightError(deliveryAddressTextarea);
                }
            }
            
            return isValid;
        }
        
        // Highlight error
        function highlightError(element) {
            element.classList.add('is-invalid');
            element.addEventListener('change', function() {
                this.classList.remove('is-invalid');
            }, { once: true });
        }
        
        // Handle order type changes
        function handleOrderTypeChange() {
            const selectedType = document.querySelector('input[name="selected_types"]:checked');
            console.log('Selected order type:', selectedType ? selectedType.value : 'none');

            // Update the hidden field with selected type
            const orderTypeHidden = document.getElementById('selected_types_hidden');
            if (orderTypeHidden) {
                orderTypeHidden.value = selectedType ? JSON.stringify([selectedType.value]) : JSON.stringify([]);
            }

            if (!selectedType) {
                // إخفاء جميع الحقول إذا لم يتم اختيار نوع
                if (contractField) contractField.style.display = 'none';
                if (contractFileField) contractFileField.style.display = 'none';
                if (orderStatusField) orderStatusField.style.display = 'none';
                return;
            }

            const selectedValue = selectedType.value;

            // إظهار/إخفاء حقول العقد للأنواع المطلوبة
            const contractRequiredTypes = ['installation', 'tailoring', 'accessory'];
            const needsContract = contractRequiredTypes.includes(selectedValue);

            if (contractField) {
                contractField.style.display = needsContract ? 'block' : 'none';
            }

            if (contractFileField) {
                contractFileField.style.display = needsContract ? 'block' : 'none';
            }

            // إظهار/إخفاء حقل وضع الطلب (مخفي للمعاينة)
            if (orderStatusField) {
                orderStatusField.style.display = selectedValue === 'inspection' ? 'none' : 'block';
            }

            // منطق التسليم حسب نوع الطلب
            if (selectedValue === 'tailoring') {
                // تحديد استلام من الفرع تلقائياً للتفصيل
                const deliveryOptionBranch = document.getElementById('delivery_option_branch');
                if (deliveryOptionBranch) {
                    deliveryOptionBranch.checked = true;
                    handleDeliveryOptionChange();
                }

                // تحديد الفرع بناءً على المستخدم
                const branchSelect = document.getElementById('{{ form.branch.id_for_label }}');
                if (branchSelect && branchSelect.options.length > 1) {
                    // إذا كان هناك أكثر من فرع، اختر الأول (أو حسب منطق المستخدم)
                    if (!branchSelect.value) {
                        branchSelect.selectedIndex = 1; // اختيار أول فرع (تجاهل الخيار الفارغ)
                    }
                }
            } else if (selectedValue === 'installation') {
                // تفعيل التوصيل للمنزل تلقائياً للتركيب
                const deliveryOptionHome = document.getElementById('delivery_option_home');
                if (deliveryOptionHome) {
                    deliveryOptionHome.checked = true;
                    handleDeliveryOptionChange();

                    // ملء العنوان من بيانات العميل تلقائياً
                    const customerSelect = document.getElementById('{{ form.customer.id_for_label }}');
                    if (customerSelect && customerSelect.value) {
                        // يمكن إضافة منطق لجلب عنوان العميل من API
                        fillCustomerAddress(customerSelect.value);
                    }
                }
            }

            // Set invoice number as required
            const invoiceInput = document.getElementById('{{ form.invoice_number.id_for_label }}');
            if (invoiceInput) {
                invoiceInput.required = true;
            }
            
            // إظهار/إخفاء قسم اختيار المنتجات للإكسسوار فقط
            if (productSelectionSection) {
                if (selectedValue === 'accessory') {
                    productSelectionSection.style.display = 'block';
                    loadProducts();
                } else {
                    productSelectionSection.style.display = 'none';
                }
            }

            // إزالة رسائل الخطأ السابقة
            const errorMsg = document.querySelector('.tailoring-installation-error');
            if (errorMsg) {
                errorMsg.remove();
            }

            // إزالة تنسيق الخطأ من قسم أنواع الطلب
            const orderTypeSection = document.querySelector('.mb-4:has([name="selected_types"])');
            if (orderTypeSection) {
                orderTypeSection.classList.remove('border', 'border-danger', 'p-2', 'rounded');
            }
        }

        // دالة لملء عنوان العميل تلقائياً
        function fillCustomerAddress(customerId) {
            if (!customerId) return;

            // يمكن إضافة استدعاء API لجلب بيانات العميل
            // fetch(`/api/customers/${customerId}/`)
            //     .then(response => response.json())
            //     .then(data => {
            //         const addressField = document.getElementById('{{ form.delivery_address.id_for_label }}');
            //         if (addressField && data.address) {
            //             addressField.value = data.address;
            //         }
            //     });
        }

        // دالة لتحديث تاريخ التسليم المتوقع
        function updateExpectedDeliveryDate() {
            const statusSelect = document.getElementById('{{ form.status.id_for_label }}');
            const expectedDateField = document.getElementById('{{ form.expected_delivery_date.id_for_label }}');

            if (!statusSelect || !expectedDateField) return;

            const status = statusSelect.value;
            const today = new Date();
            let daysToAdd = 15; // افتراضي للعادي

            if (status === 'vip') {
                daysToAdd = 7;
            }

            // حساب التاريخ المتوقع
            const expectedDate = new Date(today);
            expectedDate.setDate(today.getDate() + daysToAdd);

            // تنسيق التاريخ للحقل
            const formattedDate = expectedDate.toISOString().split('T')[0];
            expectedDateField.value = formattedDate;
        }
        
        // Handle delivery option changes
        function handleDeliveryOptionChange() {
            if (deliveryOptionHome && deliveryOptionHome.checked) {
                if (customerAddressField) customerAddressField.style.display = 'block';
                if (branchSelectionField) branchSelectionField.style.display = 'none';
            } else if (deliveryOptionBranch && deliveryOptionBranch.checked) {
                if (customerAddressField) customerAddressField.style.display = 'none';
                if (branchSelectionField) branchSelectionField.style.display = 'block';
            }
        }
        
        // Load products
        function loadProducts() {
            const productsContainer = document.getElementById('products-container');
            if (!productsContainer) return;
            
            // Show loading indicator
            const productsLoading = document.getElementById('products-loading');
            if (productsLoading) productsLoading.style.display = 'block';
            
            // Fetch products from API
            fetch('/inventory/api/products/')
                .then(response => response.json())
                .then(data => {
                    // Process products
                    displayProducts(data);
                })
                .catch(error => {
                    console.error('Error fetching products:', error);
                    if (productsContainer) {
                        productsContainer.innerHTML = '<div class="col-12"><div class="alert alert-danger">حدث خطأ أثناء تحميل المنتجات</div></div>';
                    }
                });
        }
        
        // Display products
        function displayProducts(products) {
            const productsContainer = document.getElementById('products-container');
            const productsLoading = document.getElementById('products-loading');
            
            // Hide loading indicator
            if (productsLoading) productsLoading.style.display = 'none';
            
            // Clear container
            if (productsContainer) productsContainer.innerHTML = '';
            
            if (!products || products.length === 0) {
                if (productsContainer) {
                    productsContainer.innerHTML = '<div class="col-12"><p class="text-center">لا توجد منتجات متاحة</p></div>';
                }
                return;
            }
            
            // Display products
            products.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'col-md-4 mb-3';
                productCard.innerHTML = `
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">${product.name}</h5>
                            <p class="card-text">السعر: ${product.price || 'غير محدد'}</p>
                            <div class="form-check">
                                <input class="form-check-input product-checkbox" type="checkbox" value="${product.id}" 
                                    data-name="${product.name}" data-price="${product.price || 0}" data-type="${product.product_type || 'fabric'}">
                                <label class="form-check-label">
                                    اختيار
                                </label>
                            </div>
                        </div>
                    </div>
                `;
                
                if (productsContainer) productsContainer.appendChild(productCard);
            });
            
            // Add event listeners to checkboxes
            document.querySelectorAll('.product-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectedProducts);
            });
        }
        
        // Update selected products
        function updateSelectedProducts() {
            const selectedProducts = Array.from(document.querySelectorAll('.product-checkbox:checked'));
            const selectedProductsContainer = document.getElementById('selected-products-container');
            const noProductsMessage = document.getElementById('no-products-message');
            
            if (!selectedProductsContainer || !noProductsMessage) return;
            
            if (selectedProducts.length === 0) {
                noProductsMessage.style.display = 'block';
                selectedProductsContainer.innerHTML = '';
                selectedProductsContainer.appendChild(noProductsMessage);
                return;
            }
            
            noProductsMessage.style.display = 'none';
            
            // Create table for selected products
            const table = document.createElement('table');
            table.className = 'table table-bordered';
            table.innerHTML = `
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>إجراءات</th>
                    </tr>
                </thead>
                <tbody id="selected-products-tbody"></tbody>
            `;
            
            selectedProductsContainer.innerHTML = '';
            selectedProductsContainer.appendChild(table);
            
            const tbody = document.getElementById('selected-products-tbody');
            
            selectedProducts.forEach(product => {
                const tr = document.createElement('tr');
                tr.dataset.productId = product.value;
                
                const price = parseFloat(product.dataset.price) || 0;
                
                tr.innerHTML = `
                    <td>${product.dataset.name}</td>
                    <td>${price.toFixed(2)}</td>
                    <td>
                        <input type="number" class="form-control form-control-sm product-quantity" 
                            min="1" value="1" data-product-id="${product.value}">
                    </td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-product" data-product-id="${product.value}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                
                tbody.appendChild(tr);
            });
            
            // Add event listeners to quantity inputs
            document.querySelectorAll('.product-quantity').forEach(input => {
                input.addEventListener('change', updateSelectedProductsInput);
            });
            
            // Add event listeners to remove buttons
            document.querySelectorAll('.remove-product').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.dataset.productId;
                    document.querySelector(`.product-checkbox[value="${productId}"]`).checked = false;
                    updateSelectedProducts();
                });
            });
            
            // Update selected products input
            updateSelectedProductsInput();
        }
        
        // Update selected products input
        function updateSelectedProductsInput() {
            const selectedProducts = Array.from(document.querySelectorAll('.product-checkbox:checked'));
            const selectedProductsInput = document.getElementById('selected_products');
            
            if (!selectedProductsInput) return;
            
            const productsData = selectedProducts.map(product => {
                const productId = product.value;
                const quantityInput = document.querySelector(`.product-quantity[data-product-id="${productId}"]`);
                const quantity = quantityInput ? parseInt(quantityInput.value) || 1 : 1;
                
                return {
                    id: productId,
                    name: product.dataset.name,
                    price: parseFloat(product.dataset.price) || 0,
                    type: product.dataset.type,
                    quantity: quantity
                };
            });
            
            selectedProductsInput.value = JSON.stringify(productsData);
        }

        // Initial calls
        handleOrderTypeChange();
        handleDeliveryOptionChange();
        updateExpectedDeliveryDate();
    });
</script>
{% endblock %}
