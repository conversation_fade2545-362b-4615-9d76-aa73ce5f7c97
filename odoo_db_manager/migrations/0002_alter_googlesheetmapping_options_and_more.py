# Generated by Django 4.2.21 on 2025-06-28 12:06

import datetime
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('odoo_db_manager', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='googlesheetmapping',
            options={'verbose_name': 'تعيين Google Sheets', 'verbose_name_plural': 'تعيينات Google Sheets'},
        ),
        migrations.AlterModelOptions(
            name='googlesyncschedule',
            options={'ordering': ['-created_at'], 'verbose_name': 'جدولة مزامنة', 'verbose_name_plural': 'جدولة المزامنة'},
        ),
        migrations.AlterUniqueTogether(
            name='googlesheetmapping',
            unique_together=set(),
        ),
        migrations.RemoveField(
            model_name='googlesyncconflict',
            name='conflict_description',
        ),
        migrations.RemoveField(
            model_name='googlesyncconflict',
            name='record_id',
        ),
        migrations.RemoveField(
            model_name='googlesyncconflict',
            name='record_type',
        ),
        migrations.RemoveField(
            model_name='googlesyncconflict',
            name='resolved_by',
        ),
        migrations.RemoveField(
            model_name='googlesyncconflict',
            name='sheet_row',
        ),
        migrations.RemoveField(
            model_name='googlesyncconflict',
            name='system_data',
        ),
        migrations.RemoveField(
            model_name='googlesyncschedule',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='googlesyncschedule',
            name='frequency_minutes',
        ),
        migrations.RemoveField(
            model_name='googlesyncschedule',
            name='updated_at',
        ),
        migrations.RemoveField(
            model_name='googlesynctask',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='googlesynctask',
            name='error_details',
        ),
        migrations.RemoveField(
            model_name='googlesynctask',
            name='error_message',
        ),
        migrations.RemoveField(
            model_name='googlesynctask',
            name='failed_rows',
        ),
        migrations.RemoveField(
            model_name='googlesynctask',
            name='is_scheduled',
        ),
        migrations.RemoveField(
            model_name='googlesynctask',
            name='next_run',
        ),
        migrations.RemoveField(
            model_name='googlesynctask',
            name='processed_rows',
        ),
        migrations.RemoveField(
            model_name='googlesynctask',
            name='results',
        ),
        migrations.RemoveField(
            model_name='googlesynctask',
            name='schedule_frequency',
        ),
        migrations.RemoveField(
            model_name='googlesynctask',
            name='successful_rows',
        ),
        migrations.RemoveField(
            model_name='googlesynctask',
            name='total_rows',
        ),
        migrations.AddField(
            model_name='googlesheetmapping',
            name='conflict_resolution',
            field=models.CharField(choices=[('skip', 'تجاهل التعارضات'), ('overwrite', 'الكتابة فوق البيانات الموجودة'), ('manual', 'الحل اليدوي للتعارضات')], default='manual', max_length=20, verbose_name='حل التعارضات'),
        ),
        migrations.AddField(
            model_name='googlesheetmapping',
            name='data_validation_rules',
            field=models.JSONField(blank=True, default=dict, verbose_name='قواعد التحقق من البيانات'),
        ),
        migrations.AddField(
            model_name='googlesheetmapping',
            name='row_filter_conditions',
            field=models.JSONField(blank=True, default=dict, verbose_name='شروط فلترة الصفوف'),
        ),
        migrations.AddField(
            model_name='googlesheetmapping',
            name='update_existing',
            field=models.BooleanField(default=True, verbose_name='تحديث الموجود'),
        ),
        migrations.AddField(
            model_name='googlesyncconflict',
            name='existing_data',
            field=models.JSONField(blank=True, null=True, verbose_name='البيانات الموجودة'),
        ),
        migrations.AddField(
            model_name='googlesyncconflict',
            name='resolution_action',
            field=models.CharField(blank=True, max_length=100, verbose_name='إجراء الحل'),
        ),
        migrations.AddField(
            model_name='googlesyncconflict',
            name='row_number',
            field=models.PositiveIntegerField(default=1, verbose_name='رقم الصف'),
        ),
        migrations.AddField(
            model_name='googlesyncconflict',
            name='suggested_action',
            field=models.CharField(blank=True, max_length=100, verbose_name='الإجراء المقترح'),
        ),
        migrations.AddField(
            model_name='googlesyncschedule',
            name='frequency',
            field=models.CharField(choices=[('once', 'مرة واحدة'), ('daily', 'يومياً'), ('weekly', 'أسبوعياً'), ('monthly', 'شهرياً'), ('hourly', 'كل ساعة')], default='daily', max_length=20, verbose_name='التكرار'),
        ),
        migrations.AddField(
            model_name='googlesyncschedule',
            name='name',
            field=models.CharField(default='جدولة افتراضية', max_length=200, verbose_name='اسم الجدولة'),
        ),
        migrations.AddField(
            model_name='googlesyncschedule',
            name='scheduled_time',
            field=models.TimeField(default=datetime.time(0, 0), verbose_name='وقت التنفيذ'),
        ),
        migrations.AddField(
            model_name='googlesyncschedule',
            name='task_parameters',
            field=models.JSONField(blank=True, default=dict, verbose_name='معاملات المهمة'),
        ),
        migrations.AddField(
            model_name='googlesyncschedule',
            name='task_type',
            field=models.CharField(choices=[('import', 'استيراد من Google Sheets'), ('export', 'تصدير إلى Google Sheets'), ('sync_bidirectional', 'مزامنة ثنائية الاتجاه')], default='import', max_length=20, verbose_name='نوع المهمة'),
        ),
        migrations.AddField(
            model_name='googlesynctask',
            name='error_log',
            field=models.TextField(blank=True, verbose_name='سجل الأخطاء'),
        ),
        migrations.AddField(
            model_name='googlesynctask',
            name='result',
            field=models.JSONField(blank=True, null=True, verbose_name='النتيجة'),
        ),
        migrations.AddField(
            model_name='googlesynctask',
            name='rows_failed',
            field=models.PositiveIntegerField(default=0, verbose_name='الصفوف الفاشلة'),
        ),
        migrations.AddField(
            model_name='googlesynctask',
            name='rows_processed',
            field=models.PositiveIntegerField(default=0, verbose_name='الصفوف المعالجة'),
        ),
        migrations.AddField(
            model_name='googlesynctask',
            name='rows_successful',
            field=models.PositiveIntegerField(default=0, verbose_name='الصفوف الناجحة'),
        ),
        migrations.AddField(
            model_name='googlesynctask',
            name='task_parameters',
            field=models.JSONField(blank=True, default=dict, verbose_name='معاملات المهمة'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='auto_create_customers',
            field=models.BooleanField(default=True, verbose_name='إنشاء عملاء تلقائياً'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='auto_create_inspections',
            field=models.BooleanField(default=False, verbose_name='إنشاء معاينات تلقائياً'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='auto_create_installations',
            field=models.BooleanField(default=False, verbose_name='إنشاء تركيبات تلقائياً'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='auto_create_orders',
            field=models.BooleanField(default=True, verbose_name='إنشاء طلبات تلقائياً'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='column_mappings',
            field=models.JSONField(default=dict, verbose_name='تعيينات الأعمدة'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='enable_reverse_sync',
            field=models.BooleanField(default=False, verbose_name='تمكين المزامنة العكسية'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='header_row',
            field=models.PositiveIntegerField(default=1, verbose_name='صف العناوين'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='last_row_processed',
            field=models.PositiveIntegerField(blank=True, null=True, verbose_name='آخر صف تم معالجته'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='name',
            field=models.CharField(max_length=200, verbose_name='اسم التعيين'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='reverse_sync_fields',
            field=models.JSONField(default=list, verbose_name='حقول المزامنة العكسية'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='sheet_name',
            field=models.CharField(default='Sheet1', max_length=200, verbose_name='اسم الورقة'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='spreadsheet_id',
            field=models.CharField(max_length=500, verbose_name='معرف جدول Google'),
        ),
        migrations.AlterField(
            model_name='googlesheetmapping',
            name='start_row',
            field=models.PositiveIntegerField(default=2, verbose_name='صف البداية'),
        ),
        migrations.AlterField(
            model_name='googlesyncconflict',
            name='conflict_type',
            field=models.CharField(choices=[('duplicate_customer', 'عميل مكرر'), ('duplicate_order', 'طلب مكرر'), ('data_mismatch', 'عدم تطابق البيانات'), ('missing_reference', 'مرجع مفقود'), ('validation_error', 'خطأ في التحقق')], max_length=30, verbose_name='نوع التعارض'),
        ),
        migrations.AlterField(
            model_name='googlesyncconflict',
            name='resolution_status',
            field=models.CharField(choices=[('pending', 'في الانتظار'), ('resolved', 'تم الحل'), ('ignored', 'متجاهل')], default='pending', max_length=20, verbose_name='حالة الحل'),
        ),
        migrations.AlterField(
            model_name='googlesyncconflict',
            name='sheet_data',
            field=models.JSONField(verbose_name='بيانات الجدول'),
        ),
        migrations.AlterField(
            model_name='googlesyncconflict',
            name='task',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conflicts', to='odoo_db_manager.googlesynctask', verbose_name='المهمة'),
        ),
        migrations.AlterField(
            model_name='googlesyncschedule',
            name='failed_runs',
            field=models.PositiveIntegerField(default=0, verbose_name='التنفيذ الفاشل'),
        ),
        migrations.AlterField(
            model_name='googlesyncschedule',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='نشط'),
        ),
        migrations.AlterField(
            model_name='googlesyncschedule',
            name='last_run',
            field=models.DateTimeField(blank=True, null=True, verbose_name='آخر تنفيذ'),
        ),
        migrations.AlterField(
            model_name='googlesyncschedule',
            name='mapping',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='odoo_db_manager.googlesheetmapping', verbose_name='التعيين'),
        ),
        migrations.AlterField(
            model_name='googlesyncschedule',
            name='next_run',
            field=models.DateTimeField(blank=True, null=True, verbose_name='التنفيذ القادم'),
        ),
        migrations.AlterField(
            model_name='googlesyncschedule',
            name='successful_runs',
            field=models.PositiveIntegerField(default=0, verbose_name='التنفيذ الناجح'),
        ),
        migrations.AlterField(
            model_name='googlesyncschedule',
            name='total_runs',
            field=models.PositiveIntegerField(default=0, verbose_name='إجمالي التنفيذ'),
        ),
        migrations.AlterField(
            model_name='googlesynctask',
            name='completed_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء'),
        ),
        migrations.AlterField(
            model_name='googlesynctask',
            name='mapping',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sync_tasks', to='odoo_db_manager.googlesheetmapping', verbose_name='التعيين'),
        ),
        migrations.AlterField(
            model_name='googlesynctask',
            name='started_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='تاريخ البداية'),
        ),
        migrations.AlterField(
            model_name='googlesynctask',
            name='status',
            field=models.CharField(choices=[('pending', 'في الانتظار'), ('running', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('failed', 'فشلت'), ('cancelled', 'ملغية')], default='pending', max_length=20, verbose_name='الحالة'),
        ),
        migrations.AlterField(
            model_name='googlesynctask',
            name='task_type',
            field=models.CharField(choices=[('import', 'استيراد من Google Sheets'), ('export', 'تصدير إلى Google Sheets'), ('sync_bidirectional', 'مزامنة ثنائية الاتجاه')], max_length=20, verbose_name='نوع المهمة'),
        ),
        migrations.AlterModelTable(
            name='googlesheetmapping',
            table='google_sheet_mapping',
        ),
        migrations.AlterModelTable(
            name='googlesyncconflict',
            table='google_sync_conflict',
        ),
        migrations.AlterModelTable(
            name='googlesyncschedule',
            table='google_sync_schedule',
        ),
        migrations.AlterModelTable(
            name='googlesynctask',
            table='google_sync_task',
        ),
        migrations.RemoveField(
            model_name='googlesheetmapping',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='googlesheetmapping',
            name='default_branch',
        ),
        migrations.RemoveField(
            model_name='googlesheetmapping',
            name='default_customer_category',
        ),
        migrations.RemoveField(
            model_name='googlesheetmapping',
            name='default_customer_type',
        ),
        migrations.RemoveField(
            model_name='googlesheetmapping',
            name='update_existing_customers',
        ),
        migrations.RemoveField(
            model_name='googlesheetmapping',
            name='update_existing_orders',
        ),
        migrations.RemoveField(
            model_name='googlesheetmapping',
            name='use_current_date_as_created',
        ),
    ]
