# Generated by Django 4.2.21 on 2025-06-28 13:17

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
        ('customers', '0001_initial'),
        ('odoo_db_manager', '0003_googlesynctask_created_by'),
    ]

    operations = [
        migrations.AddField(
            model_name='googlesheetmapping',
            name='default_branch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounts.branch', verbose_name='الفرع الافتراضي'),
        ),
        migrations.AddField(
            model_name='googlesheetmapping',
            name='default_customer_category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='customers.customercategory', verbose_name='تصنيف العميل الافتراضي'),
        ),
        migrations.AddField(
            model_name='googlesheetmapping',
            name='default_customer_type',
            field=models.CharField(blank=True, choices=[('retail', 'أفراد'), ('wholesale', 'جملة'), ('corporate', 'شركات')], max_length=20, null=True, verbose_name='نوع العميل الافتراضي'),
        ),
        migrations.AddField(
            model_name='googlesheetmapping',
            name='use_current_date_as_created',
            field=models.BooleanField(default=False, verbose_name='استخدام التاريخ الحالي كتاريخ الإضافة'),
        ),
    ]
