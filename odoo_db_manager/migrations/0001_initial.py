# Generated by Django 4.2.21 on 2025-06-28 11:17

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('customers', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Database',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم قاعدة البيانات')),
                ('db_type', models.CharField(choices=[('postgresql', 'PostgreSQL')], max_length=20, verbose_name='نوع قاعدة البيانات')),
                ('connection_info', models.JSONField(default=dict, verbose_name='معلومات الاتصال')),
                ('is_active', models.BooleanField(default=False, verbose_name='نشطة')),
                ('status', models.BooleanField(default=False, verbose_name='حالة الاتصال')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='رسالة الخطأ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قاعدة بيانات',
                'verbose_name_plural': 'قواعد البيانات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleSheetMapping',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم التعيين')),
                ('spreadsheet_id', models.CharField(max_length=255, verbose_name='معرف جدول البيانات')),
                ('sheet_name', models.CharField(max_length=100, verbose_name='اسم الصفحة')),
                ('column_mappings', models.JSONField(default=dict, help_text='تعيين أعمدة Google Sheets إلى حقول النظام', verbose_name='تعيين الأعمدة')),
                ('auto_create_customers', models.BooleanField(default=True, verbose_name='إنشاء العملاء تلقائياً')),
                ('auto_create_orders', models.BooleanField(default=True, verbose_name='إنشاء الطلبات تلقائياً')),
                ('auto_create_inspections', models.BooleanField(default=True, verbose_name='إنشاء المعاينات تلقائياً')),
                ('auto_create_installations', models.BooleanField(default=False, verbose_name='إنشاء التركيبات تلقائياً')),
                ('update_existing_customers', models.BooleanField(default=True, verbose_name='تحديث العملاء الموجودين')),
                ('update_existing_orders', models.BooleanField(default=True, verbose_name='تحديث الطلبات الموجودة')),
                ('enable_reverse_sync', models.BooleanField(default=False, verbose_name='تفعيل المزامنة العكسية')),
                ('reverse_sync_fields', models.JSONField(default=list, help_text='الحقول التي سيتم مزامنتها عكسياً إلى Google Sheets', verbose_name='حقول المزامنة العكسية')),
                ('default_customer_type', models.CharField(blank=True, choices=[('retail', 'أفراد'), ('wholesale', 'جملة'), ('corporate', 'شركات')], help_text='نوع العميل الذي سيتم تعيينه للعملاء الجدد إذا لم يكن محدد في الجدول', max_length=20, null=True, verbose_name='نوع العميل الافتراضي')),
                ('use_current_date_as_created', models.BooleanField(default=True, help_text='استخدام تاريخ المزامنة كتاريخ إضافة للسجلات الجديدة', verbose_name='استخدام التاريخ الحالي كتاريخ الإضافة')),
                ('header_row', models.IntegerField(default=1, verbose_name='صف العناوين')),
                ('start_row', models.IntegerField(default=2, verbose_name='صف البداية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('last_sync', models.DateTimeField(blank=True, null=True, verbose_name='آخر مزامنة')),
                ('last_row_processed', models.IntegerField(default=0, verbose_name='آخر صف تمت معالجته')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('default_branch', models.ForeignKey(blank=True, help_text='الفرع الذي سيتم تعيينه للعملاء والطلبات الجديدة إذا لم يكن محدد في الجدول', null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounts.branch', verbose_name='الفرع الافتراضي')),
                ('default_customer_category', models.ForeignKey(blank=True, help_text='التصنيف الذي سيتم تعيينه للعملاء الجدد إذا لم يكن محدد في الجدول', null=True, on_delete=django.db.models.deletion.SET_NULL, to='customers.customercategory', verbose_name='تصنيف العميل الافتراضي')),
            ],
            options={
                'verbose_name': 'تعيين Google Sheets',
                'verbose_name_plural': 'تعيينات Google Sheets',
                'ordering': ['-created_at'],
                'unique_together': {('spreadsheet_id', 'sheet_name')},
            },
        ),
        migrations.CreateModel(
            name='GoogleSyncConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='الاسم')),
                ('spreadsheet_id', models.CharField(max_length=255, verbose_name='معرف جدول البيانات')),
                ('credentials_file', models.FileField(upload_to='google_credentials/', verbose_name='ملف بيانات الاعتماد')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('last_sync', models.DateTimeField(blank=True, null=True, verbose_name='آخر مزامنة')),
                ('sync_frequency', models.IntegerField(default=24, verbose_name='تكرار المزامنة (بالساعات)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('sync_databases', models.BooleanField(default=True, verbose_name='مزامنة قواعد البيانات')),
                ('sync_users', models.BooleanField(default=True, verbose_name='مزامنة المستخدمين')),
                ('sync_customers', models.BooleanField(default=True, verbose_name='مزامنة العملاء')),
                ('sync_orders', models.BooleanField(default=True, verbose_name='مزامنة الطلبات')),
                ('sync_products', models.BooleanField(default=True, verbose_name='مزامنة المنتجات')),
                ('sync_settings', models.BooleanField(default=True, verbose_name='مزامنة الإعدادات')),
                ('sync_inspections', models.BooleanField(default=True, verbose_name='مزامنة المعاينات')),
            ],
            options={
                'verbose_name': 'إعداد مزامنة غوغل',
                'verbose_name_plural': 'إعدادات مزامنة غوغل',
            },
        ),
        migrations.CreateModel(
            name='ImportLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sheet_name', models.CharField(max_length=100, verbose_name='اسم الجدول')),
                ('total_records', models.IntegerField(default=0, verbose_name='إجمالي السجلات')),
                ('imported_records', models.IntegerField(default=0, verbose_name='السجلات المستوردة')),
                ('updated_records', models.IntegerField(default=0, verbose_name='السجلات المحدثة')),
                ('failed_records', models.IntegerField(default=0, verbose_name='السجلات الفاشلة')),
                ('clear_existing', models.BooleanField(default=False, verbose_name='حذف البيانات القديمة')),
                ('status', models.CharField(choices=[('success', 'نجح'), ('failed', 'فشل'), ('partial', 'جزئي')], default='success', max_length=20, verbose_name='الحالة')),
                ('error_details', models.TextField(blank=True, verbose_name='تفاصيل الأخطاء')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_import_logs', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='assigned_import_logs', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'سجل استيراد',
                'verbose_name_plural': 'سجلات الاستيراد',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleSyncTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('task_type', models.CharField(choices=[('import', 'استيراد من Google Sheets'), ('export', 'تصدير إلى Google Sheets'), ('sync', 'مزامنة ثنائية الاتجاه'), ('reverse_sync', 'مزامنة عكسية')], max_length=20, verbose_name='نوع المهمة')),
                ('status', models.CharField(choices=[('pending', 'في الانتظار'), ('running', 'قيد التنفيذ'), ('completed', 'مكتمل'), ('failed', 'فشل'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('started_at', models.DateTimeField(blank=True, null=True, verbose_name='بدء التنفيذ')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='انتهاء التنفيذ')),
                ('total_rows', models.IntegerField(default=0, verbose_name='إجمالي الصفوف')),
                ('processed_rows', models.IntegerField(default=0, verbose_name='الصفوف المعالجة')),
                ('successful_rows', models.IntegerField(default=0, verbose_name='الصفوف الناجحة')),
                ('failed_rows', models.IntegerField(default=0, verbose_name='الصفوف الفاشلة')),
                ('results', models.JSONField(default=dict, help_text='تفاصيل نتائج المزامنة', verbose_name='نتائج التنفيذ')),
                ('error_message', models.TextField(blank=True, verbose_name='رسالة الخطأ')),
                ('error_details', models.JSONField(default=list, verbose_name='تفاصيل الأخطاء')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('is_scheduled', models.BooleanField(default=False, verbose_name='مجدولة')),
                ('schedule_frequency', models.IntegerField(default=60, verbose_name='تكرار الجدولة (بالدقائق)')),
                ('next_run', models.DateTimeField(blank=True, null=True, verbose_name='التشغيل القادم')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('mapping', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sync_tasks', to='odoo_db_manager.googlesheetmapping', verbose_name='تعيين الصفحة')),
            ],
            options={
                'verbose_name': 'مهمة مزامنة',
                'verbose_name_plural': 'مهام المزامنة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleSyncSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=False, verbose_name='نشط')),
                ('frequency_minutes', models.IntegerField(choices=[(1, 'كل دقيقة'), (5, 'كل 5 دقائق'), (15, 'كل 15 دقيقة'), (30, 'كل 30 دقيقة'), (60, 'كل ساعة'), (360, 'كل 6 ساعات'), (720, 'كل 12 ساعة'), (1440, 'يومياً')], default=60, verbose_name='التكرار (بالدقائق)')),
                ('last_run', models.DateTimeField(blank=True, null=True, verbose_name='آخر تشغيل')),
                ('next_run', models.DateTimeField(blank=True, null=True, verbose_name='التشغيل القادم')),
                ('total_runs', models.IntegerField(default=0, verbose_name='إجمالي مرات التشغيل')),
                ('successful_runs', models.IntegerField(default=0, verbose_name='مرات التشغيل الناجحة')),
                ('failed_runs', models.IntegerField(default=0, verbose_name='مرات التشغيل الفاشلة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('mapping', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='schedule', to='odoo_db_manager.googlesheetmapping', verbose_name='تعيين الصفحة')),
            ],
            options={
                'verbose_name': 'جدولة المزامنة',
                'verbose_name_plural': 'جدولة المزامنة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleSyncLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('success', 'نجاح'), ('error', 'خطأ'), ('warning', 'تحذير')], max_length=20, verbose_name='الحالة')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('details', models.JSONField(blank=True, default=dict, verbose_name='التفاصيل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('config', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='odoo_db_manager.googlesyncconfig')),
            ],
            options={
                'verbose_name': 'سجل مزامنة غوغل',
                'verbose_name_plural': 'سجلات مزامنة غوغل',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleSyncConflict',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('conflict_type', models.CharField(choices=[('data_mismatch', 'عدم تطابق البيانات'), ('concurrent_edit', 'تعديل متزامن'), ('missing_record', 'سجل مفقود'), ('validation_error', 'خطأ في التحقق')], max_length=20, verbose_name='نوع التعارض')),
                ('resolution_status', models.CharField(choices=[('pending', 'في الانتظار'), ('resolved_system', 'تم الحل لصالح النظام'), ('resolved_sheet', 'تم الحل لصالح Google Sheets'), ('resolved_manual', 'تم الحل يدوياً'), ('ignored', 'تم التجاهل')], default='pending', max_length=20, verbose_name='حالة الحل')),
                ('record_type', models.CharField(max_length=50, verbose_name='نوع السجل')),
                ('record_id', models.CharField(blank=True, max_length=100, verbose_name='معرف السجل')),
                ('sheet_row', models.IntegerField(verbose_name='رقم الصف في الجدول')),
                ('system_data', models.JSONField(default=dict, verbose_name='بيانات النظام')),
                ('sheet_data', models.JSONField(default=dict, verbose_name='بيانات Google Sheets')),
                ('conflict_description', models.TextField(verbose_name='وصف التعارض')),
                ('resolution_notes', models.TextField(blank=True, verbose_name='ملاحظات الحل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الحل')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الحل بواسطة')),
                ('task', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='conflicts', to='odoo_db_manager.googlesynctask', verbose_name='مهمة المزامنة')),
            ],
            options={
                'verbose_name': 'تعارض مزامنة',
                'verbose_name_plural': 'تعارضات المزامنة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GoogleDriveConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='إعدادات Google Drive', max_length=100, verbose_name='اسم الإعداد')),
                ('inspections_folder_id', models.CharField(blank=True, help_text='معرف المجلد في Google Drive لحفظ ملفات المعاينات', max_length=255, verbose_name='معرف مجلد المعاينات')),
                ('inspections_folder_name', models.CharField(blank=True, help_text='اسم المجلد في Google Drive', max_length=255, verbose_name='اسم مجلد المعاينات')),
                ('credentials_file', models.FileField(blank=True, help_text='ملف JSON من Google Cloud Console', null=True, upload_to='google_credentials/', verbose_name='ملف اعتماد Google')),
                ('filename_pattern', models.CharField(default='{customer}_{branch}_{date}_{order}', help_text='المتغيرات المتاحة: {customer}, {branch}, {date}, {order}', max_length=200, verbose_name='نمط تسمية الملفات')),
                ('is_active', models.BooleanField(default=True, verbose_name='مفعل')),
                ('last_test', models.DateTimeField(blank=True, null=True, verbose_name='آخر اختبار')),
                ('test_status', models.CharField(blank=True, max_length=50, verbose_name='حالة الاختبار')),
                ('test_message', models.TextField(blank=True, verbose_name='رسالة الاختبار')),
                ('total_uploads', models.IntegerField(default=0, verbose_name='إجمالي الرفعات')),
                ('last_upload', models.DateTimeField(blank=True, null=True, verbose_name='آخر رفع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'إعدادات Google Drive',
                'verbose_name_plural': 'إعدادات Google Drive',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BackupSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الجدولة')),
                ('backup_type', models.CharField(choices=[('customers', 'بيانات العملاء'), ('users', 'بيانات المستخدمين'), ('settings', 'إعدادات النظام'), ('full', 'كل البيانات')], default='full', max_length=20, verbose_name='نوع النسخة الاحتياطية')),
                ('frequency', models.CharField(choices=[('hourly', 'كل ساعة'), ('daily', 'يومياً'), ('weekly', 'أسبوعياً'), ('monthly', 'شهرياً')], default='daily', max_length=20, verbose_name='التكرار')),
                ('hour', models.IntegerField(default=0, help_text='0-23', verbose_name='الساعة')),
                ('minute', models.IntegerField(default=0, help_text='0-59', verbose_name='الدقيقة')),
                ('day_of_week', models.IntegerField(blank=True, choices=[(0, 'الاثنين'), (1, 'الثلاثاء'), (2, 'الأربعاء'), (3, 'الخميس'), (4, 'الجمعة'), (5, 'السبت'), (6, 'الأحد')], default=0, null=True, verbose_name='يوم الأسبوع')),
                ('day_of_month', models.IntegerField(blank=True, default=1, help_text='1-31', null=True, verbose_name='يوم الشهر')),
                ('max_backups', models.IntegerField(default=24, help_text='الحد الأقصى هو 24 نسخة', verbose_name='الحد الأقصى لعدد النسخ')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('last_run', models.DateTimeField(blank=True, null=True, verbose_name='آخر تشغيل')),
                ('next_run', models.DateTimeField(blank=True, null=True, verbose_name='التشغيل القادم')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='backup_schedules', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('database', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='backup_schedules', to='odoo_db_manager.database', verbose_name='قاعدة البيانات')),
            ],
            options={
                'verbose_name': 'جدولة النسخ الاحتياطية',
                'verbose_name_plural': 'جدولة النسخ الاحتياطية',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Backup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم النسخة الاحتياطية')),
                ('file_path', models.CharField(max_length=255, verbose_name='مسار الملف')),
                ('size', models.BigIntegerField(default=0, verbose_name='الحجم (بايت)')),
                ('backup_type', models.CharField(choices=[('customers', 'بيانات العملاء'), ('users', 'بيانات المستخدمين'), ('settings', 'إعدادات النظام'), ('full', 'كل البيانات')], default='full', max_length=20, verbose_name='نوع النسخة الاحتياطية')),
                ('is_scheduled', models.BooleanField(default=False, verbose_name='مجدولة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('database', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='backups', to='odoo_db_manager.database', verbose_name='قاعدة البيانات')),
            ],
            options={
                'verbose_name': 'نسخة احتياطية',
                'verbose_name_plural': 'النسخ الاحتياطية',
                'ordering': ['-created_at'],
            },
        ),
    ]
